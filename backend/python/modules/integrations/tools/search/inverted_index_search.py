from typing import List
import json
import xml.etree.ElementTree as ET
from collections import defaultdict
from core.config import get_config
from modules.integrations.tools.search.search_tool import SearchToolABC
from modules.common.schema import CodeSnippet
from utils.file import generate_file_hash_name
from modules.term.term import save_inverted_index
from utils.trace_logger import get_trace_logger
from utils.convert import safe_parse_xml_with_preprocessing, extract_text_from_xml_element

# 获取支持 trace 的 logger
logger = get_trace_logger(__name__)


class InvertedIndexSearchTool(SearchToolABC):
    def __init__(self, repo_path: str, cache_dir: str = get_config().data.cache_dir, refresh: bool = True, chunk_keywords_num: int = 20):
        self.repo_path = repo_path

        # 加载缓存文件  
        self.term_sparse_file_path = save_inverted_index(project_dir=repo_path, 
                                                           cache_dir=cache_dir, 
                                                           project_id=generate_file_hash_name(repo_path), 
                                                           refresh=refresh,
                                                           chunk_keywords_num=chunk_keywords_num
                                                          )
        
        # 加载缓存文件
        with open(self.term_sparse_file_path, 'r', encoding='utf-8') as f:
            keywords_cache = json.load(f)
            self.keywords2chunk = keywords_cache['keywords2chunk']
            self.chunk_path2idx = keywords_cache['chunk_path2idx']
            self.chunk_content = keywords_cache['chunk_content']
            
    def search(self, query: str, **kwargs) -> List[CodeSnippet]:
        """
        使用倒排索引搜索代码片段，支持XML格式的查询参数

        Args:
            query: 查询字符串，可以是XML格式或普通文本

        Returns:
            List[CodeSnippet]: 搜索结果
        """
        try:
            # 解析查询参数
            search_params = self._parse_query(query)

            # 获取查询关键词和top_k参数
            query_terms = search_params['query_terms']
            top_k = search_params['top_k']

            # 获取所有关键词对应的chunk
            chunk_scores = defaultdict(int)
            for term in query_terms:
                if term in self.keywords2chunk:
                    for chunk_idx in self.keywords2chunk[term]:
                        chunk_scores[self.chunk_path2idx[chunk_idx]] += 1

            # 按照chunk进行排序
            sorted_chunks = sorted(chunk_scores.items(), key=lambda x: x[1], reverse=True)[:min(top_k, len(chunk_scores))]

            return [
                CodeSnippet(
                    file_path=chunk_path.split(":")[0],
                    start_line=int(chunk_path.split(":")[1].split("-")[0]),
                    end_line=int(chunk_path.split(":")[1].split("-")[1]),
                    content=self.chunk_content[chunk_path],
                    context_before="",
                    context_after="",
                    score=score
                )
                for chunk_path, score in sorted_chunks
            ]
        except Exception as e:
            logger.error(f"Inverted index search failed: {e}")
            return []

    def _parse_query(self, query: str) -> dict:
        """
        解析查询参数，支持XML格式和普通文本

        Args:
            query: 查询字符串

        Returns:
            dict: 解析后的参数字典
        """
        # 默认参数
        params = {
            'query_terms': query.strip().split(),
            'top_k': 20
        }

        # 尝试解析XML格式
        if query.strip().startswith('<inverted_index>'):
            try:
                # 解析XML
                root = safe_parse_xml_with_preprocessing(query.strip(), 'query')

                # 提取查询关键词
                query_elem = root.find('query')
                if query_elem is not None and query_elem.text:
                    query_content = extract_text_from_xml_element(query_elem)
                    params['query_terms'] = query_content.strip().split()

                # 提取top_k参数
                top_k_elem = root.find('top_k')
                if top_k_elem is not None and top_k_elem.text:
                    try:
                        params['top_k'] = int(top_k_elem.text.strip())
                    except ValueError:
                        logger.warning(f"Invalid top_k value: {top_k_elem.text}, using default 20")

            except ET.ParseError as e:
                logger.warning(f"Failed to parse XML query, using as plain text: {e}")

        return params

    @property
    def description(self):
        return """- ``inverted_index``: Keyword-based search engine using inverted index for multi-keyword content discovery. Designed for finding content that relates to any of several related keywords you provide.

**Primary Use Case**:
- **Multi-keyword Search**: When you have several related keywords and want to find content that matches any of them

**Key Features**:
- Pre-built inverted index mapping keywords to code chunks
- Finds content related to ANY of the provided keywords
- Fast retrieval through keyword-to-chunk mapping
- Returns results ranked by keyword relevance scores

**When to Use This Tool**:
- ✅ You have multiple related keywords (3-10 keywords work best)
- ✅ You want to find content that relates to any of these keywords
- ✅ You know specific technical terms, API names, or domain vocabulary
- ✅ You're exploring a topic area with several related concepts
- ❌ Don't use for single keyword searches or vague descriptions

**Parameters**:
- query: Multiple related keywords separated by spaces
- top_k: (optional) Maximum results to return (default: 20)

**Query Style**: Provide multiple related keywords that describe different aspects of what you're looking for. The tool will find content related to any of these keywords.

**XML Format Support**: You can use XML format for structured queries:
```xml
<inverted_index>
<query>keyword1 keyword2 keyword3</query>
<top_k>15</top_k>
</inverted_index>
```"""
    
    @property
    def examples(self):
        return """<output>
    <inverted_index>
    <query>http client request response async session timeout headers authentication</query>
    <top_k>20</top_k>
    </inverted_index>

    <inverted_index>
    <query>database connection SQL query transaction commit rollback cursor execute</query>
    <top_k>15</top_k>
    </inverted_index>

    <inverted_index>
    <query>configuration setup installation deployment environment variables settings</query>
    </inverted_index>

    <inverted_index>
    <query>authentication login password JWT token validation security hash encrypt</query>
    <top_k>10</top_k>
    </inverted_index>
</output>"""
